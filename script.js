// For Loop function that counts from 1-10 with 0.5 second delay
async function runForLoop() {
    const output = document.querySelector('.for-loop-container .output');
    output.innerHTML = ''; // Clear previous output

    for (let i = 1; i <= 10; i++) {
        // Add the current number to the output
        output.innerHTML += `${i} `;

        // Wait for 0.5 seconds before the next iteration
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Add completion message
    output.innerHTML += '<br><strong>Loop completed!</strong>';
}

// While Loop function (placeholder for future implementation)
function runWhileLoop() {
    const output = document.querySelector('.while-loop-container .output');
    output.innerHTML = 'While loop not implemented yet';
}

// forEach Loop function (placeholder for future implementation)
function runForEachLoop() {
    const output = document.querySelector('.forEach-loop-container .output');
    output.innerHTML = 'forEach loop not implemented yet';
}