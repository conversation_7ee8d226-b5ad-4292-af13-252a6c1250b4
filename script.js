// For Loop function that counts from 1-10 with 0.5 second delay
async function runForLoop() {
    const output = document.querySelector('.for-loop-container .output');
    const button = document.querySelector('.for-loop-container button');

    // Clear previous output and reset button to "Run"
    output.innerHTML = '';
    button.textContent = 'Run';
    button.disabled = true; // Disable button during loop execution

    for (let i = 1; i <= 10; i++) {
        // Add the current number to the output
        output.innerHTML += `${i} `;

        // Wait for 0.5 seconds before the next iteration
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Add completion message and change button to "Restart"
    output.innerHTML += '<br><strong>Loop completed!</strong>';
    button.textContent = 'Restart';
    button.disabled = false; // Re-enable button
}

// While Loop function (placeholder for future implementation)
function runWhileLoop() {
    const output = document.querySelector('.while-loop-container .output');
    output.innerHTML = 'While loop not implemented yet';
}

// forEach Loop function (placeholder for future implementation)
function runForEachLoop() {
    const output = document.querySelector('.forEach-loop-container .output');
    output.innerHTML = 'forEach loop not implemented yet';
}