// For Loop function that counts from 1-10 with 0.5 second delay
async function runForLoop() {
    const output = document.querySelector('.for-loop-container .output');
    const button = document.querySelector('.for-loop-container button');

    // Clear previous output and reset button to "Run"
    output.innerHTML = '';
    button.textContent = 'Run';
    button.disabled = true; // Disable button during loop execution

    for (let i = 1; i <= 10; i++) {
        // Add the current number to the output
        output.innerHTML += `${i} `;

        // Wait for 0.5 seconds before the next iteration
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Add completion message and change button to "Restart"
    output.innerHTML += '<br><strong>Loop completed!</strong>';
    button.textContent = 'Restart';
    button.disabled = false; // Re-enable button
}

// While Loop function that adds random numbers until total > 30
async function runWhileLoop() {
    const output = document.querySelector('.while-loop-container .output');
    const button = document.querySelector('.while-loop-container button');

    // Clear previous output and reset button
    output.innerHTML = '';
    button.textContent = 'Run';
    button.disabled = true; // Disable button during loop execution

    let total = 0;
    let step = 1;

    output.innerHTML = '<strong>Adding random numbers until total > 30:</strong><br><br>';

    while (total <= 30) {
        // Generate random number between 1 and 10
        const randomNum = Math.floor(Math.random() * 10) + 1;
        total += randomNum;

        // Display the step, random number, and running total
        output.innerHTML += `Step ${step}: +${randomNum} → Total: ${total}<br>`;
        step++;

        // Wait for 0.8 seconds before the next iteration
        await new Promise(resolve => setTimeout(resolve, 800));
    }

    // Add completion message and change button to "Restart"
    output.innerHTML += '<br><strong>🎉 Goal reached! Total exceeded 30!</strong>';
    button.textContent = 'Restart';
    button.disabled = false; // Re-enable button
}

// forEach Loop function that displays colors with matching backgrounds
async function runForEachLoop() {
    const output = document.querySelector('.forEach-loop-container .output');
    const button = document.querySelector('.forEach-loop-container button');

    // Clear previous output and reset button
    output.innerHTML = '';
    button.textContent = 'Run';
    button.disabled = true; // Disable button during loop execution

    // Array of colors to display
    const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan', 'lime', 'magenta'];

    output.innerHTML = '<strong>Displaying colors with matching backgrounds:</strong><br><br>';

    // Use forEach with async/await pattern
    for (let i = 0; i < colors.length; i++) {
        const color = colors[i];

        // Create a span element with the color name and matching background
        const colorSpan = `<span style="background-color: ${color}; color: white; padding: 8px 12px; margin: 4px; border-radius: 4px; display: inline-block; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">${color.toUpperCase()}</span>`;

        output.innerHTML += colorSpan + '<br>';

        // Wait for 0.6 seconds before showing the next color
        await new Promise(resolve => setTimeout(resolve, 600));
    }

    // Add completion message and change button to "Restart"
    output.innerHTML += '<br><strong>🌈 All colors displayed!</strong>';
    button.textContent = 'Restart';
    button.disabled = false; // Re-enable button
}